import { WidgetUtils } from './widgetUtils';
import { SceneNode, TextNode, FrameNode, ElementData } from '../types/figma';
import { widgetSettings, displayNameMap } from '../config/widgetSettings';
import { ElementTypeResolver } from './elementTypeResolver';
import { isFeatureEnabled } from '../config/appConfig';

interface ProcessArgs {
  titleTagKey?: string;
  titleTag?: string;
  showTitleKey?: string;
  showTitle?: string;
  showButtonKey?: string;
  showButton?: string;
}

export class Utils {
  public static findNodeRecursively(node: SceneNode, parentNode: SceneNode | undefined, nodeName: string, widgetType?: string): SceneNode | null {
    if (!node) return null;

    // Skip advanced menu widgets when looking for menu items to avoid false matches
    if (ElementTypeResolver.isWidget(node) && node.name.toLowerCase().includes('advanced') && node.name.toLowerCase().includes('menu')) {
      return null;
    }

    // 1. Primary method: Check display name map for exact matches
    if (displayNameMap[nodeName.toLowerCase()]) {
      const possibleNames = displayNameMap[nodeName.toLowerCase()];
      for (const name of possibleNames) {
        if (this.nodeNameMatches(node.name, name)) {
          // Apply font size check only for post carousel widget
          if (widgetType === 'eael-post-carousel' && node.type === 'TEXT') {
            const shouldSkip = (nodeName.toLowerCase() === 'section-title' && node.fontSize && node.fontSize < 30) ||
                              (nodeName.toLowerCase() === 'title' && node.fontSize && node.fontSize >= 30);
            if (shouldSkip) continue;
          }

          // Exclude subtitle terms when looking for title
          if (nodeName.toLowerCase() === 'title' && this.isSubtitleTerm(node.name.toLowerCase())) {
            continue;
          }

          return node;
        }
      }
    }

    // 2. Secondary method: Direct name matching
    if (this.nodeNameMatches(node.name, nodeName)) {
      // Apply font size check only for post carousel widget
      if (widgetType === 'eael-post-carousel' && node.type === 'TEXT') {
        const shouldSkip = (nodeName.toLowerCase() === 'section-title' && node.fontSize && node.fontSize < 30) ||
                          (nodeName.toLowerCase() === 'title' && node.fontSize && node.fontSize >= 30);
        if (shouldSkip) {
          // Continue searching instead of returning
        } else if (nodeName.toLowerCase() === 'title' && this.isSubtitleTerm(node.name.toLowerCase())) {
          // Continue searching instead of returning
        } else {
          return node;
        }
      } else {
        // No font size restrictions for other widgets
        if (nodeName.toLowerCase() === 'title' && this.isSubtitleTerm(node.name.toLowerCase())) {
          // Continue searching instead of returning
        } else {
          return node;
        }
      }
    }

    // 3. Auto-detection (only if enabled via config)
    if (isFeatureEnabled('enableAutoNodeDetection') && node.type === 'TEXT' && 'characters' in node) {
      const textContent = node.characters.toLowerCase();

      // Simple content-based detection for titles
      if ((nodeName.toLowerCase() === 'title' || nodeName.toLowerCase() === 'heading') &&
          textContent.length < 50 && !textContent.includes('.') && node.fontSize && node.fontSize >= 16) {

        // Apply font size check only for post carousel widget
        if (widgetType === 'eael-post-carousel') {
          const shouldSkip = (nodeName.toLowerCase() === 'section-title' && node.fontSize < 30) ||
                            (nodeName.toLowerCase() === 'title' && node.fontSize >= 30);
          if (!shouldSkip && !this.isSubtitleTerm(node.name.toLowerCase())) {
            return node;
          }
        } else if (!this.isSubtitleTerm(node.name.toLowerCase())) {
          return node;
        }
      }

      // Simple content-based detection for content
      if ((nodeName.toLowerCase() === 'content' || nodeName.toLowerCase() === 'description') &&
          (textContent.length > 50 || textContent.includes('.'))) {
        return node;
      }
    }

    // 4. Recursive search in children
    if ('children' in node && node.children) {
      for (const child of node.children) {
        const foundNode = Utils.findNodeRecursively(child, node, nodeName, widgetType);
        if (foundNode) return foundNode;
      }
    }

    return null;
  }

  private static nodeNameMatches(nodeName: string, searchTerm: string): boolean {
    nodeName = nodeName.toLowerCase();
    searchTerm = searchTerm.toLowerCase();

    const exactMatch = nodeName === searchTerm;
    const startsWithDash = nodeName.startsWith(searchTerm + '-');
    const startsWithSpace = nodeName.startsWith(searchTerm + ' ');
    const endsWithDash = nodeName.endsWith('-' + searchTerm);
    const endsWithSpace = nodeName.endsWith(' ' + searchTerm);
    const includesDash = nodeName.includes('-' + searchTerm + '-');
    const includesSpace = nodeName.includes(' ' + searchTerm + ' ');

    return exactMatch || startsWithDash || startsWithSpace || endsWithDash || endsWithSpace || includesDash || includesSpace;
  }

  /**
   * Check if a term is subtitle-related and should not be considered as a main title
   */
  private static isSubtitleTerm(term: string): boolean {
    const normalizedTerm = term.toLowerCase().trim();

    // Exact matches for subtitle terms
    const subtitleTerms = [
      'subtitle', 'sub title', 'sub-title',
      'subheading', 'sub heading', 'sub-heading',
      'subtext', 'sub text', 'sub-text'
    ];

    // Check for exact matches or terms that START with subtitle prefixes
    return subtitleTerms.some(subtitleTerm =>
      normalizedTerm === subtitleTerm ||
      normalizedTerm.startsWith(subtitleTerm + '-') ||
      normalizedTerm.startsWith(subtitleTerm + '_') ||
      normalizedTerm.startsWith(subtitleTerm + ' ')
    );
  }

  /**
   * Find the parent node of a given node within a container
   * @param node The container to search in
   * @param childNode The node to find the parent for
   * @returns The parent node if found, null otherwise
   */
  public static findParentNode(node: SceneNode, childNode: SceneNode): SceneNode | null {
    if (!node || !childNode) return null;



    if ('children' in node && node.children) {
      // Check if the child node is a direct child of the container
      if (node.children.includes(childNode as any)) {

        return node;
      }

      // Recursively check children
      for (const child of node.children) {
        if ('children' in child && child.children) {
          const foundParent = Utils.findParentNode(child, childNode);
          if (foundParent) {
            return foundParent;
          }
        }
      }
    }

    return null;
  }

  public static findTextNodes(node: FrameNode): TextNode[] {
    const textNodes: TextNode[] = [];

    if (!node || !('children' in node) || !node.children) {
      return textNodes;
    }

    for (const child of node.children) {
      if (child.type === 'TEXT') {
        textNodes.push(child);
      } else if ('children' in child) {
        textNodes.push(...Utils.findTextNodes(child as FrameNode));
      }
    }

    return textNodes;
  }

  public static getTextWithCase(textNode: any): string {
    if (!("characters" in textNode)) return "";

    let text = textNode.characters;
    let caseType = textNode.textCase;

    switch (caseType) {
      case "UPPER":
        text = text.toUpperCase();
        break;
      case "LOWER":
        text = text.toLowerCase();
        break;
      case "TITLE":
        text = text.replace(/\b\w/g, (char) => char.toUpperCase());
        break;
      case "SMALL_CAPS":
      case "SMALL_CAPS_FORCED":
        // Small caps may require custom font handling

        break;
      default:
        // Keep original case
        break;
    }

    return text;
  }

  /*
  * Process title node
  * titleNode: SceneNode
  * data: ElementData
  * titleKey: string
  * widgetNodeType: string
  * args: ProcessArgs
  * Example:
  * Basic Usage: Utils.processTitleNode(titleNode, data, "eael_infobox_title", "eael-info-box-title");
  * Output: data.settings.eael_infobox_title = "Title";
  * With Args Usage:
  * Usage: Utils.processTitleNode(titleNode, data, "eael_infobox_title", "eael-info-box-title", { titleTagKey: 'eael_infobox_title_tag', titleTag: 'h2' });
  * Output: data.settings.eael_infobox_title = "Title"; data.settings.eael_infobox_title_tag = "h2";
  */
  public static processTitleNode(titleNode: SceneNode, data: ElementData, titleKey?: string, widgetNodeType?: string, args?: ProcessArgs) {
    if (!titleNode) {
      return;
    }

    let textNode: TextNode | null = null;

    if ('children' in titleNode && titleNode.children) {
      const foundNode = titleNode.children.find(child => child?.type === "TEXT");
      if (foundNode && foundNode.type === "TEXT") {
        textNode = foundNode;
      }
    } else if (titleNode.type === "TEXT") {
      textNode = titleNode;
    }

    if (textNode) {
      if (titleKey) {
        data.settings[titleKey] = Utils.getTextWithCase(textNode);
      }

      if (args) {
        if (args.titleTagKey && args.titleTag) {
          data.settings[args.titleTagKey] = args.titleTag;
        }

        if (args.showTitleKey && args.showTitle) {
          data.settings[args.showTitleKey] = args.showTitle;
        }
      }

      if (widgetNodeType) {
        WidgetUtils.processTypographySettings(textNode, data, widgetNodeType);
        WidgetUtils.processTextColor(textNode, data, widgetNodeType);
        if (titleNode.type === 'FRAME') {
          WidgetUtils.processMargin(titleNode, data, widgetNodeType);
        }
      }
    }
  }

  public static processRepeaterTitleNode(titleNode: SceneNode, data: ElementData, titleKey?: string, widgetNodeType?: string, args?: ProcessArgs) {
    if (!titleNode || !data || Array.isArray(data.settings)) {
      return;
    }

    let textNode: TextNode | null = null;

    if ('children' in titleNode && titleNode.children) {
      const foundNode = titleNode.children.find(child => child?.type === "TEXT");
      if (foundNode && foundNode.type === "TEXT") {
        textNode = foundNode;
      }
    } else if (titleNode.type === "TEXT") {
      textNode = titleNode;
    }

    let repeaterItem: any = {};

    if (textNode) {
      if (titleKey) {
        repeaterItem[titleKey] = Utils.getTextWithCase(textNode);
      }

      if (args) {
        if (args.titleTagKey && args.titleTag) {
          repeaterItem[args.titleTagKey] = args.titleTag;
        }

        if (args.showTitleKey && args.showTitle) {
          repeaterItem[args.showTitleKey] = args.showTitle;
        }
      }

      if (widgetNodeType) {
        if (data.widgetType && widgetSettings[data.widgetType]?.typography?.[widgetNodeType]) {
          const prefix = widgetSettings[data.widgetType].typography![widgetNodeType];
          const typography = WidgetUtils.getTypographySettings(textNode, data);

          repeaterItem[`${prefix}_typography`] = typography.typography;
          repeaterItem[`${prefix}_font_family`] = typography.fontFamily;
          repeaterItem[`${prefix}_text_decoration`] = typography.textDecoration;
          repeaterItem[`${prefix}_font_weight`] = typography.fontWeight;
          repeaterItem[`${prefix}_font_size`] = typography.fontSize;
          repeaterItem[`${prefix}_line_height`] = typography.lineHeight;
          repeaterItem[`${prefix}_text_transform`] = typography.textTransform;
          repeaterItem[`${prefix}_letter_spacing`] = typography.letterSpacing;
        }

        const colorSettingKey = widgetSettings[data.widgetType]?.color?.[widgetNodeType];
        if (colorSettingKey) {
          const color = WidgetUtils.getColorSettings(textNode, data);
          repeaterItem[colorSettingKey] = color;
        }
      }
    }

    return repeaterItem;
  }

  public static processIconNode(iconNode: SceneNode, data: ElementData, iconKey?: string, rotationKey?: string) {
    if (!iconNode) return;

    let iconName = iconNode.name.substring(5);
    // Extract rotation if exists
    const rotationMatch = iconName.match(/-rotation(-?\d+)$/);
    if (rotationMatch) {
      if (rotationKey) {
        data.settings[rotationKey] = {
          unit: "deg",
          size: parseInt(rotationMatch[1], 10),
          sizes: []
        };
      }
      // Remove the "-rotationXXX" part from the icon name
      iconName = iconName.replace(/-rotation(-?\d+)$/, "");
    }

    if (iconKey) {
      data.settings[iconKey] = {
        value: `fas ${iconName}`,
        library: "fa-solid",
      };
    }
  }

  public static processButtonNode(buttonNode: SceneNode, data: ElementData, buttonKey?: string, widgetNodeType?: string, args?: ProcessArgs) {
    if (!buttonNode) return;

    // Handle text-only buttons
    if (buttonNode.type === 'TEXT') {
      const textNode = buttonNode as TextNode;


      if (buttonKey && !Array.isArray(data.settings)) {
        data.settings[buttonKey] = textNode.characters;
        data.settings.eael_cta_btn_is_used = "yes";
        data.settings.eael_cta_btn_normal_border_border = "none";
      }

      if (args && !Array.isArray(data.settings)) {
        if (args.showButtonKey && args.showButton) {
          data.settings[args.showButtonKey] = args.showButton;
        }
      }

      if (widgetNodeType) {
        WidgetUtils.processTypographySettings(textNode, data, widgetNodeType);
        WidgetUtils.processTextColor(textNode, data, widgetNodeType);

        if (buttonNode.parent) {
          WidgetUtils.processBackground(buttonNode.parent, data, widgetNodeType);
          WidgetUtils.processPadding(buttonNode.parent, data, widgetNodeType);
          WidgetUtils.processBorderRadius(buttonNode.parent, data, widgetNodeType);
          WidgetUtils.processBorderWidth(buttonNode.parent, data, widgetNodeType);
          WidgetUtils.processBoxShadow(buttonNode.parent, data, widgetNodeType);
        }
      }

      return;
    }

    // Handle frame-based buttons
    const textNodes = Utils.findTextNodes(buttonNode as FrameNode);
    const buttonText: string[] = [];

    if (textNodes.length > 0) {
      textNodes.forEach((textNode) => {
        buttonText.push(textNode.characters);
      });

      if (buttonKey && !Array.isArray(data.settings)) {
        data.settings[buttonKey] = buttonText.join(" ");
        // data.settings.eael_cta_btn_is_used = "yes"; // need to check if this is correct.
        // data.settings.eael_cta_btn_normal_border_border = "none";
      }

      if (args && !Array.isArray(data.settings)) {
        if (args.showButtonKey && args.showButton) {
          data.settings[args.showButtonKey] = args.showButton;
        }
      }

      if (widgetNodeType && !Array.isArray(data.settings)) {
        // Process text styling
        WidgetUtils.processTypographySettings(textNodes[0], data, widgetNodeType);
        WidgetUtils.processTextColor(textNodes[0], data, widgetNodeType);

        // Process button styling if it's a frame
        if (buttonNode.type === 'FRAME') {
          // Process background only if it has fills
          if (buttonNode.fills && buttonNode.fills.length > 0) {
            WidgetUtils.processBackground(buttonNode, data, widgetNodeType);
          }

          WidgetUtils.processPadding(buttonNode, data, widgetNodeType);

          // Process border radius if it has corner radius
          if (buttonNode.cornerRadius !== undefined && buttonNode.cornerRadius > 0) {
            WidgetUtils.processBorderRadius(buttonNode, data, widgetNodeType);
          }

          // Process border width if it has strokes
          if (buttonNode.strokes && buttonNode.strokes.length > 0) {
            WidgetUtils.processBorderWidth(buttonNode, data, widgetNodeType);
          }

          // Process box shadow
          WidgetUtils.processBoxShadow(buttonNode, data, widgetNodeType);
        }
      }
    }
  }

  static getButtonActionPattern(): RegExp {
    const buttonWords = this.getButtonWords();
    return new RegExp(`^(${buttonWords.join('|')})`, 'i');
  }

  public static getButtonWords(): string[] {
    return [
      'submit', 'send', 'click', 'buy', 'learn', 'read', 'view', 'download', 'contact', 'book', 'call',
      'schedule', 'sign up', 'register', 'apply',
      'check out', 'add to cart', 'reserve', 'book now', 'call now', 'contact us',
      'get started', 'learn more', 'read more', 'view more', 'download now'
    ];
  }
}