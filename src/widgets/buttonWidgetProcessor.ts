import { Utils } from '../utils/utils';
import { SceneNode, TextNode } from '../types/figma';
import { WidgetUtils } from '../utils/widgetUtils';

export class ButtonWidgetProcessor {
  public static process(node: any, data: any) {
    let textNode: TextNode | undefined;
    let iconNode: SceneNode | undefined;

    if (node.type === "TEXT") {
      textNode = node;
    } else if ("children" in node && Array.isArray(node.children)) {
      // find text node from any level of children
      const textNodes = Utils.findTextNodes(node);

      if (textNodes.length > 0) {
        textNode = textNodes[0];
      }
      
      iconNode = node.children?.find((child: SceneNode) => child.name.startsWith('icon-'));
    }

    Utils.processTitleNode(textNode, data, 'text', 'button-text');
    Utils.processIconNode(iconNode, data, 'selected_icon');

    data.settings.icon_align =
      "children" in node &&
        node.children &&
        node.children.length >= 2 &&
        node.children[0].type === "TEXT" &&
        node.children[1].name.startsWith("icon-")
        ? "row-reverse"
        : "row";

    const buttonParentNode = node.parent;
    let buttonParentAlignmentItems;
    let buttonParentAlignment = "";
    if (buttonParentNode && "layoutMode" in buttonParentNode) {
      const buttonParentLayoutMode = buttonParentNode.layoutMode;
      if (buttonParentLayoutMode === "VERTICAL" &&
        "counterAxisAlignItems" in buttonParentNode) {
        buttonParentAlignmentItems = buttonParentNode.counterAxisAlignItems;
      }
      else if (buttonParentLayoutMode === "HORIZONTAL" &&
        "counterAxisAlignItems" in buttonParentNode) {
        buttonParentAlignmentItems = buttonParentNode.counterAxisAlignItems;
      }
    }

    buttonParentAlignment =
      buttonParentAlignmentItems === "MAX"
        ? "flex-end"
        : buttonParentAlignmentItems === "CENTER"
          ? "center"
          : buttonParentAlignmentItems === "MIN"
            ? "flex-start"
            : buttonParentAlignment;

    if (buttonParentAlignment) {
      data.settings._flex_align_self = buttonParentAlignment;
    }

    // process button background
    const containerNode = node.type === "TEXT" ? node.parent : node;

    // if container node has no fill, check immediate (first?) child for bg; [note: in advanced menu we check immediate parent.]
    if (!containerNode.fills || containerNode.fills.length === 0) {
      if (containerNode.children && containerNode.children.length > 0 
          && "fills" in containerNode.children[0] && containerNode.children[0].fills.length > 0) {
        WidgetUtils.processBackground(containerNode.children?.[0], data, 'button-container');
      }
    } else {
      WidgetUtils.processBackground(containerNode, data, 'button-container');
    }
  }
} 