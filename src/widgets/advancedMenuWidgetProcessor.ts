import { WidgetUtils } from '../utils/widgetUtils';
import { Utils } from '../utils/utils';
import { ElementData, SceneNode, FrameNode, TextNode } from '../types/figma';

export class AdvancedMenuWidgetProcessor {
  public static process(node: FrameNode, data: ElementData): void {
    // Process menu ID

    if (data.widgetType.startsWith('eael-advanced-menu') && data.widgetType.length > 18) {
      const menuIdMatch = data.widgetType.match(/(\d+)$/);
      if (menuIdMatch) {
        data.settings.eael_advanced_menu_menu = menuIdMatch[1];
      }
      data.widgetType = 'eael-advanced-menu';
    } else {
      // Default menu ID if not specified
      if (!data.settings.eael_advanced_menu_menu) {
        data.settings.eael_advanced_menu_menu = "1"; // Default menu ID
      }
    }

    // Find menu items - try with explicit naming first, then try to detect automatically
    let menuItemNode = Utils.findNodeRecursively(node, undefined, "menu-item");
    let menuItemActiveNode = Utils.findNodeRecursively(node, undefined, "active-menu-item");

    // If no explicitly named menu items found, try to detect them by structure
    if (!menuItemNode && node.children && node.children.length > 0) {
      // Look for horizontal or vertical list of text items that could be menu items
      const possibleMenuItems = node.children.filter(child =>
        child.type === 'FRAME' &&
        child.children &&
        child.children.some(grandchild => grandchild.type === 'TEXT')
      );

      if (possibleMenuItems.length > 0) {
        menuItemNode = possibleMenuItems[0];

        // If we have multiple items, the one with different styling might be active
        if (possibleMenuItems.length > 1) {
          // Look for an item with different fill color or stroke that might be active
          for (let i = 1; i < possibleMenuItems.length; i++) {
            const item = possibleMenuItems[i];
            if (item.fills && item.fills.length > 0) {
              menuItemActiveNode = item;
              break;
            }
          }
        }
      }
    }

    // Use the main node as container if no specific container found
    const containerNode = Utils.findNodeRecursively(node, undefined, "widget-eael-advanced-menu") || node;

    // Process container background. if no fill, check parent for bg
    if (!containerNode.fills || containerNode.fills.length === 0) {
      WidgetUtils.processBackground(containerNode.parent, data, "eael-advanced-menu-container");
    } else {
      WidgetUtils.processBackground(containerNode, data, "eael-advanced-menu-container");
    }

    // Process menu item text and typography
    if (menuItemNode) {

      const menuItemTextNode = (menuItemNode as FrameNode).children?.find(child => child.type === "TEXT") as SceneNode & { characters?: string };
      if (menuItemTextNode) {
        if (menuItemTextNode.type === "TEXT") {
          const textNode = menuItemTextNode as unknown as TextNode;

          WidgetUtils.processTextColor(textNode, data, "eael-advanced-menu-item");
          WidgetUtils.processTypographySettings(textNode, data, "eael-advanced-menu-item");
        }
      }
    }

    // Process active menu item text and background
    if (menuItemActiveNode) {
      const menuItemActiveTextNode = (menuItemActiveNode as FrameNode).children?.find(child => child.type === "TEXT") as SceneNode & { characters?: string };
      if (menuItemActiveTextNode) {
        WidgetUtils.processTextColor(menuItemActiveTextNode, data, "eael-advanced-menu-item-hover");
        WidgetUtils.processBackground(menuItemActiveNode, data, "eael-advanced-menu-item-hover");
      }
    }

    // Process layout and default settings
    if (!Array.isArray(data.settings)) {
      data.settings.default_eael_advanced_menu_layout = "layoutMode" in node ? node.layoutMode.toLowerCase() : "horizontal";
      WidgetUtils.processBackground(node, data, "eael-advanced-menu-container");
    }

    // Process height
    if (node.height) {
      data.settings.height = 'min-height';
      data.settings.custom_height = {
        unit: 'px',
        size: node.height,
        sizes: []
      };
    }

    // Process margin and padding
    WidgetUtils.processMargin(node, data);
    WidgetUtils.processPadding(node, data);
  }
}